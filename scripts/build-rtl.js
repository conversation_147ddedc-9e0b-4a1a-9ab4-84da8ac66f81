const path = require('path');
const fs = require('fs');
const sass = require('sass');
const rtlcss = require('rtlcss');

// Define the SCSS files to process
const entries = {
    'style': './resources/pos/src/assets/scss/style.scss',
    'custom': './resources/pos/src/assets/scss/custom/custom.scss',
    'frontend': './resources/pos/src/assets/scss/frontend/frontend.scss',
};

// Output directory
const outputDir = './resources/pos/src/assets/css';

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Process each SCSS file
Object.entries(entries).forEach(([name, inputFile]) => {
    try {
        console.log(`Processing ${name}...`);
        
        // Compile SCSS to CSS
        const result = sass.compile(inputFile, {
            includePaths: [
                'node_modules',
                'resources/pos/src/assets/scss',
                'resources/pos/src/assets/scss/components',
                'resources/pos/src/assets/scss/base',
                'resources/pos/src/assets/scss/layout',
                'resources/pos/src/assets/scss/custom',
                'resources/pos/src/assets/scss/frontend',
            ],
            style: 'expanded'
        });

        // Write regular CSS file
        const cssFile = path.join(outputDir, `${name}.css`);
        fs.writeFileSync(cssFile, result.css);
        console.log(`✓ Generated ${cssFile}`);

        // Generate RTL CSS
        const rtlCss = rtlcss.process(result.css);
        const rtlCssFile = path.join(outputDir, `${name}.rtl.css`);
        fs.writeFileSync(rtlCssFile, rtlCss);
        console.log(`✓ Generated ${rtlCssFile}`);

    } catch (error) {
        console.error(`Error processing ${name}:`, error.message);
    }
});

console.log('RTL CSS generation completed!');
