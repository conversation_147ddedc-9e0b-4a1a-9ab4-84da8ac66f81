import { defineConfig, transformWithEsbuild } from "vite";
import laravel from "laravel-vite-plugin";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

export default defineConfig({
    plugins: [
        {
            name: "load+transform-js-files-as-jsx",
            async transform(code, id) {
                if (!id.match(/src\/.*\.js$/)) {
                    return null;
                }

                // Use the exposed transform from vite, instead of directly
                // transforming with esbuild
                return transformWithEsbuild(code, id, {
                    loader: "jsx",
                    jsx: "automatic",
                });
            },
        },
        laravel({
            input: [
                "resources/css/app.css",
                "resources/js/app.jsx",
                "resources/assets/js/custom.js",
            ],
            refresh: true,
        }),
        react(),
    ],
    resolve: {
        alias: {
            "@": resolve(__dirname, "resources"),
        },
    },
    esbuild: {
        loader: "jsx",
        include: /resources\/.*\.(js|jsx)$/, // ⬅️ Tell esbuild to treat these .js and .jsx files as JSX
    },
    build: {
        outDir: "public/build",
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ["react", "react-dom"],
                    redux: [
                        "redux",
                        "react-redux",
                        "redux-thunk",
                        "redux-persist",
                    ],
                    router: ["react-router", "react-router-dom"],
                    ui: ["react-bootstrap", "reactstrap", "bootstrap"],
                    charts: [
                        "chart.js",
                        "react-chartjs-2",
                        "echarts",
                        "echarts-for-react",
                    ],
                },

            },
        },
        copyPublicDir: false,
    },
    publicDir: "resources/images",
    server: {
        hmr: {
            host: "localhost",
        },
    },
    assetsInclude: [
        "**/*.png",
        "**/*.jpg",
        "**/*.jpeg",
        "**/*.gif",
        "**/*.svg",
        "**/*.ico",
    ],
    optimizeDeps: {
        force: true,
        esbuildOptions: {
            loader: {
                ".js": "jsx",
            },
        },
    },
    css: {
        preprocessorOptions: {
            scss: {
                api: 'modern-compiler',
            }
        }
    },
});
