@import "~react-datepicker/dist/react-datepicker.css";
@import "react-toastify/dist/ReactToastify.css";
.datepicker__custom-datepicker {
  width: 100%;
  padding: 2.5px;
  border-radius: 0.475rem;
  height: 45px;
  border: 1px solid #ced4da;
  box-shadow: unset;
  font-family: Pop<PERSON>s, Helvetica, "sans-serif";
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
  outline: none;
  cursor: pointer;
}
.datepicker .react-datepicker {
  background-color: #ffffff !important;
  border: 0 !important;
  border-radius: 0.475rem !important;
  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.1) !important;
  font-family: inherit !important;
}
@media (max-width: 576px) {
  .datepicker .react-datepicker {
    left: 10px;
  }
}
.datepicker .react-datepicker__header {
  fill: #7e8299;
  border-bottom: 0 !important;
  background: transparent !important;
  color: #7e8299;
  height: 70px;
  padding: 0.5rem 1rem 0 !important;
  line-height: 1;
  text-align: center !important;
  position: relative !important;
  user-select: none;
  overflow: hidden;
  flex: 1;
  width: 281px;
}
@media (max-width: 576px) {
  .datepicker .react-datepicker__header {
    width: 261px !important;
  }
}
.datepicker .react-datepicker__current-month {
  font-weight: 500 !important;
  position: absolute;
  right: 23.5%;
  line-height: 1;
  height: 34px;
  display: inline-block;
  text-align: center;
  transform: translate3d(0, 0, 0);
  padding: 7.48px 0 0 0;
  font-size: 1rem !important;
  color: #212529;
  width: 145px;
}
.datepicker .react-datepicker__day-names {
  position: relative;
  top: 20px;
  background: 100% 0;
  text-align: center;
  overflow: hidden;
  display: flex;
  margin-top: 20px;
}
.datepicker .react-datepicker__day-name {
  width: 36px !important;
  height: 36px !important;
  color: #3f4254;
  font-size: 0;
  font-weight: 600;
}
.datepicker .react-datepicker__day-name:first-letter {
  font-size: 14px;
}
.datepicker .react-datepicker__triangle:before {
  content: none !important;
}
.datepicker .react-datepicker__triangle:after {
  content: none !important;
}
.datepicker .react-datepicker__day {
  box-shadow: none !important;
  color: #495057;
  margin: 0 !important;
  line-height: 36px !important;
  width: 35px !important;
  height: 35px;
  border-radius: 0.313rem;
}
.datepicker .react-datepicker__navigation-icon {
  top: 5px !important;
}
.datepicker .react-datepicker__navigation-icon:before {
  border-width: 1px 0 0 1px !important;
}
.datepicker .react-datepicker__day--selected {
  background: #e0e3ff !important;
  color: #0015ff !important;
}
.datepicker .react-datepicker__day--today {
  font-weight: 400 !important;
}

@media (min-width: 576px) {
  .custom-dateRange-picker {
    max-width: 250px;
  }
}

.date-input.form-control {
  padding: 0.57rem 0.938rem !important;
}

.react-datepicker-popper {
  padding-top: 0 !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #0015ff !important;
  color: #ffffff !important;
}

.input-icon {
  position: absolute;
  left: 14px;
  top: 49%;
  transform: translateY(-50%);
  pointer-events: none;
  right: auto;
}

.date-input {
  cursor: pointer;
  font-weight: 600 !important;
}

.date-picker-popover .list-group .list-group-item {
  cursor: pointer;
  padding: 7px 20px !important;
}

@media (max-width: 575px) {
  .date-picker__child-popover .popover {
    right: 300px !important;
    min-width: 200px;
  }
}

@media (max-width: 480px) {
  .filter-dropdown .dropdown-menu {
    min-width: 242px !important;
  }
}

.form-control[readonly] {
  background-color: #ffffff !important;
}

.rdt_TableHeader {
  border-bottom: 1px solid #e9ecef;
  padding: 2rem 2.25rem !important;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}
@media (max-width: 925px) {
  .rdt_TableHeader {
    border: 0;
  }
}

.search-icon {
  z-index: 1;
}

.rdt_Table {
  margin-top: 1.25rem !important;
  border-radius: 0.625rem;
  box-shadow: 0 5px 20px rgba(173, 181, 189, 0.2);
}
@media (max-width: 1200px) {
  .rdt_Table {
    overflow: overlay;
  }
}
@media (max-width: 705px) {
  .rdt_Table {
    overflow: visible;
  }
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef;
  border-top-right-radius: 0.625rem;
  border-top-left-radius: 0.625rem;
}
@media (max-width: 925px) {
  .rdt_Table .rdt_TableHead .rdt_TableHeadRow {
    border: 0;
  }
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol {
  color: #6c757d !important;
  text-transform: uppercase !important;
  text-align: right !important;
  font-size: 0.875rem;
  font-weight: normal !important;
  padding-right: 0;
}
@media (max-width: 925px) {
  .rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef;
  }
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
  padding-right: 30px !important;
  white-space: normal !important;
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:last-child {
  padding-left: 30px !important;
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol .rdt_TableCol_Sortable {
  justify-content: space-between;
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol .rdt_TableCol_Sortable:hover {
  opacity: 1;
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:hover {
  color: #0015ff !important;
}
.rdt_Table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol span.__rdt_custom_sort_icon__ i {
  font-size: 10px;
  width: auto;
  height: auto;
}
.rdt_Table .rdt_TableBody .rdt_TableRow {
  color: #3f4254;
  border-bottom: 1px solid #e9ecef;
  padding: 10px 0;
}
@media (max-width: 925px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow {
    border: 0;
  }
}
.rdt_Table .rdt_TableBody .rdt_TableRow:nth-child(even) {
  background-color: #f8f9fa;
}
@media (max-width: 925px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow:nth-child(even) {
    padding: 0;
    background-color: transparent !important;
  }
}
@media (max-width: 925px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow:nth-child(even) .rdt_TableCell {
    background-color: #f8f9fa !important;
    border-top: 0.5px solid #e9ecef;
    border-bottom: 0.5px solid #e9ecef;
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
.rdt_Table .rdt_TableBody .rdt_TableRow:last-child {
  border-bottom-right-radius: 0.625rem;
  border-bottom-left-radius: 0.625rem;
  border-bottom: 0;
}
.rdt_Table .rdt_TableBody .rdt_TableRow .btn-transparent {
  background: none !important;
  padding: 0 !important;
  border: 0;
}
.rdt_Table .rdt_TableBody .rdt_TableRow .btn-transparent:hover, .rdt_Table .rdt_TableBody .rdt_TableRow .btn-transparent:active, .rdt_Table .rdt_TableBody .rdt_TableRow .btn-transparent:focus {
  background: none !important;
}
.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell {
  font-size: 0.875rem;
  padding-right: 0;
  width: 100px;
}
.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
  padding-right: 30px;
}
@media (max-width: 1199px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    padding-right: 23px;
  }
}
@media (max-width: 705px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    padding-right: 30px;
  }
}
.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:last-child {
  padding-left: 30px;
}
@media (max-width: 1199px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:last-child {
    padding-left: 23px;
  }
}
@media (max-width: 705px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:last-child {
    padding-left: 30px;
  }
}
.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell .dropdown-menu.show {
  transform: translate(0, 40px) !important;
  padding: 10px !important;
  min-width: 180px !important;
  height: auto;
  min-height: 155px;
  display: inline-block;
  box-sizing: border-box;
  z-index: 99999 !important;
}
.rdt_Table .rdt_TableCol_Sortable {
  justify-content: start;
}

div .rdt_Pagination {
  border-top: 0 !important;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
  padding-right: 0 !important;
  padding-left: 0 !important;
  margin-top: 30px;
  min-height: 0 !important;
  background-color: #eff3f7 !important;
  font-size: 1rem !important;
}
div .rdt_Pagination div:nth-child(4) {
  margin-right: auto !important;
}
div .rdt_Pagination div:nth-child(4) button {
  background-color: #eff3f7 !important;
  border-color: #eff3f7 !important;
  fill: #6c757d !important;
  border-radius: 6px !important;
  margin-right: 6px !important;
  width: 38px !important;
  height: 38px !important;
}
div .rdt_Pagination div:nth-child(4) button svg {
  width: 23px !important;
  height: 23px !important;
}
div .rdt_Pagination div:nth-child(4) button:hover, div .rdt_Pagination div:nth-child(4) button:focus {
  fill: #0015ff !important;
  background-color: #e9ecef !important;
  border-color: #e9ecef !important;
}
div .rdt_Pagination div:first-of-type select {
  background-color: #ffffff;
  padding: 6px 20px 6px 35px;
  border-radius: 8px;
}
div .rdt_Pagination div:first-of-type svg {
  top: 5px;
  left: 11px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  background-color: #ebedf3;
  border-color: #ebedf3;
}
.form-check-input:checked[type=checkbox] {
  background-size: auto;
}

#pagination-first-page {
  outline: none;
}
#pagination-first-page:hover:not(:disabled) svg {
  fill: #ffffff;
}
#pagination-first-page:focus {
  border: 1px solid #0015ff;
}

.Custom-product-img {
  object-fit: cover;
}
@media (max-width: 576px) {
  .Custom-product-img {
    height: 300px;
  }
}

@media (max-width: 1200px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    width: 1000px;
  }
}

@media (max-width: 873px) {
  .rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell {
    word-break: break-word !important;
  }
}

.bnAwAJ,
.iOIgTb {
  min-width: 149px !important;
}
@media (max-width: 1200px) {
  .bnAwAJ,
  .iOIgTb {
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -webkit-flex-basis: 0;
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    max-width: 100%;
    min-width: 175px !important;
  }
}

.data-table header {
  padding: 0;
  background-color: #eff3f7;
  align-items: start !important;
}

.data-table > :nth-child(2) {
  overflow: overlay !important;
  overflow-x: overlay !important;
  overflow-y: overlay !important;
}
@media (max-width: 1775px) {
  .data-table > :nth-child(2) {
    overflow: overlay !important;
    overflow-x: overlay !important;
    overflow-y: overlay !important;
  }
}

.ifOHjV,
.gcPjVa {
  position: relative;
}
@media (max-width: 1200px) {
  .ifOHjV,
  .gcPjVa {
    display: table !important;
  }
}
@media (max-width: 705px) {
  .ifOHjV,
  .gcPjVa {
    display: table !important;
  }
}

@media (max-width: 821px) {
  .purchases_table .data-table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .purchases_table .data-table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .purchases_table .data-table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .purchases_table .data-table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
    min-width: 139px !important;
  }
}

@media (max-width: 821px) {
  .purchases_return_table .data-table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .purchases_return_table .data-table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    min-width: 139px !important;
  }
}

@media (max-width: 821px) {
  .sale_table .data-table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .sale_table .data-table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    min-width: 139px !important;
  }
}

@media (max-width: 821px) {
  .sale_return_table .data-table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
    min-width: 139px !important;
  }
}
@media (max-width: 821px) {
  .sale_return_table .data-table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    min-width: 139px !important;
  }
}

@media (max-width: 821px) {
  .warehouse_sale_report_table .data-table .rdt_TableHead .rdt_TableHeadRow .rdt_TableCol:first-child {
    min-width: 140px !important;
  }
}
@media (max-width: 821px) {
  .warehouse_sale_report_table .data-table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    min-width: 140px !important;
  }
}

@media (max-width: 991px) {
  .tranfer_model .modal-dialog {
    max-width: 545px !important;
  }
}
@media (max-width: 576px) {
  .tranfer_model .modal-dialog {
    max-width: 400 !important;
  }
}

.searchBox .form-control {
  line-height: 1.3 !important;
}

.rdt_TableBody .rdt_TableRow:nth-child(1) :last-child .table-dropdown .dropdown-menu {
  position: fixed !important;
  transform: translate(90px, 40px) !important;
}
.rdt_TableBody .rdt_TableRow:nth-child(2) :last-child .table-dropdown .dropdown-menu {
  position: fixed !important;
  transform: translate(90px, 40px) !important;
}

.css-26l3qy-menu,
.css-1nmdiq5-menu {
  background-color: #fff !important;
  border: 0 !important;
  border-radius: 0.475rem !important;
  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.1) !important;
  padding: 0 !important;
  margin-top: 0 !important;
  z-index: 999 !important;
  color: #6c757d !important;
}

.css-26l3qy-menu .css-1n7v3ny-option,
.css-26l3qy-menu .css-9gakcf-option,
.css-1nmdiq5-menu .css-tr4s17-option,
.css-1nmdiq5-menu .css-d7l1ni-option {
  background-color: #6571ff !important;
  cursor: pointer !important;
}

.css-26l3qy-menu .css-1n7v3ny-option:hover,
.css-26l3qy-menu .css-1n7v3ny-option:not(:active),
.css-1nmdiq5-menu .css-d7l1ni-option,
.css-1nmdiq5-menu .css-d7l1ni-option:not(:active) {
  color: #000000 !important;
  background-color: #e0e3ff !important;
}

.css-4ljt47-MenuList {
  border: 1px solid #ced4da !important;
  border-radius: 0.475rem !important;
}

.css-tlfecz-indicatorContainer .css-tj5bde-Svg,
.css-1gtu0rj-indicatorContainer .css-tj5bde-Svg {
  color: #6c757d !important;
  width: 24px !important;
  height: 24px !important;
}

.css-1s2u09g-control:hover,
.css-1pahdxg-control:hover,
.css-t3ipsp-control:hover,
.css-13cymwt-control:hover {
  border: 1px solid #ced4da !important;
  background-color: #ffffff !important;
}

.css-1pahdxg-control,
.css-t3ipsp-control {
  border-color: red !important;
}

.css-yt9ioa-option,
.css-10wo9uf-option {
  cursor: pointer !important;
}

.css-1s2u09g-control,
.css-1pahdxg-control,
.css-t3ipsp-control,
.css-1insrsq-control,
.css-13cymwt-control {
  box-shadow: unset !important;
  min-height: 45px !important;
}

.css-1s2u09g-control,
.css-1pahdxg-control,
.css-t3ipsp-control,
.css-13cymwt-control {
  background-color: #ffffff !important;
  border: 1px solid #ced4da !important;
}

.css-1okebmr-indicatorSeparator,
.css-109onse-indicatorSeparator,
.css-1u9des2-indicatorSeparator {
  width: unset !important;
}

.css-tlfecz-indicatorContainer,
.css-1gtu0rj-indicatorContainer {
  color: hsl(0, 0%, 80%) !important;
}
.css-tlfecz-indicatorContainer:hover,
.css-1gtu0rj-indicatorContainer:hover {
  color: hsl(0, 0%, 80%) !important;
}
.css-tlfecz-indicatorContainer .css-tj5bde-Svg,
.css-1gtu0rj-indicatorContainer .css-tj5bde-Svg {
  color: #a1a5b7 !important;
}

.css-319lph-ValueContainer {
  font-family: inherit !important;
}

.css-1s2u09g-control,
.css-13cymwt-control,
.css-1pahdxg-control,
.css-t3ipsp-control,
.css-1insrsq-control {
  padding: 2.5px !important;
  border-radius: 0.475rem !important;
  height: 42px !important;
}

.multi_select .css-1s2u09g-control {
  height: auto !important;
}

.css-1s2u09g-control:hover,
.css-1pahdxg-control:hover,
.css-t3ipsp-control:hover,
.css-13cymwt-control:hover {
  border: 1px solid #ced4da !important;
  background-color: #ffffff !important;
}

.css-14el2xx-placeholder,
.css-1jqq78o-placeholder {
  color: #6c757d !important;
  font-family: Poppins, Helvetica, "sans-serif" !important;
  font-size: 14px !important;
}

.css-qc6sy-singleValue {
  color: #6c757d !important;
  font-family: Poppins, Helvetica, "sans-serif" !important;
  font-size: 14px !important;
}

.css-1hb7zxy-IndicatorsContainer .css-tlfecz-indicatorContainer,
.css-1hb7zxy-IndicatorsContainer .css-1gtu0rj-indicatorContainer,
.css-1hb7zxy-IndicatorsContainer .css-tlfecz-indicatorContainer,
.css-1hb7zxy-IndicatorsContainer .css-1gtu0rj-indicatorContainer {
  padding: 6px 0 6px 6px !important;
}

.input_dropdown .form-group {
  height: 100% !important;
}
.input_dropdown .form-group .css-b62m3t-container {
  height: 100% !important;
}
.input_dropdown .form-group .css-b62m3t-container .css-13cymwt-control,
.input_dropdown .form-group .css-b62m3t-container .css-t3ipsp-control {
  height: 100% !important;
  border-radius: 0 4px 4px 0 !important;
  border-left: none !important;
  outline: none !important;
  border-color: hsl(0, 0%, 80%) !important;
  box-shadow: none !important;
}
.input_dropdown input {
  border-radius: 0.313rem 0 0 0.313rem !important;
}

.css-g1d714-ValueContainer {
  height: 35px !important;
  overflow: auto !important;
}

.custom-search {
  font-family: Poppins, Helvetica, "sans-serif";
}
.custom-search:nth-child(1) {
  position: relative !important;
  height: 46px !important;
}
.custom-search .wrapper {
  z-index: 11 !important;
  border: none !important;
  border-radius: 0.475rem !important;
  background-color: transparent !important;
  box-shadow: none !important;
  padding-right: 0 !important;
  position: absolute !important;
  display: flex !important;
  color: #212121 !important;
  font-size: 16px !important;
  flex-direction: column !important;
  width: 100% !important;
}
.custom-search .wrapper div:nth-child(2) {
  font-size: 14px !important;
  background: #ffffff !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.475rem !important;
}
.custom-search .wrapper div:nth-child(2) > .line {
  display: none !important;
  background-color: #dee2e6 !important;
  opacity: 1 !important;
  width: calc(100% - 26px) !important;
  border-top: 1px solid rgb(232, 234, 237) !important;
  margin: 0px 14px 0px 20px !important;
  padding-bottom: 4px !important;
}
.custom-search .wrapper div:nth-child(2) ul {
  padding-bottom: 0 !important;
  font-family: Poppins, Helvetica, "sans-serif" !important;
}
.custom-search .wrapper div:nth-child(2) ul li {
  position: relative !important;
  padding: 0.688rem 0.938rem !important;
}
.custom-search .wrapper div:nth-child(2) ul li:before {
  content: "\f002" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  -webkit-font-smoothing: antialiased !important;
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
  color: #6c757d !important;
}
.custom-search .wrapper div:nth-child(2) .ellipsis {
  display: flex !important;
  align-items: center !important;
  text-align: right !important;
  width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
.custom-search .wrapper div:nth-child(2) .ellipsis span {
  color: #6c757d !important;
}
.custom-search .wrapper div:nth-child(2) .selected {
  background-color: #0015ff !important;
}
.custom-search .wrapper div:nth-child(2) .selected:first-child {
  border-top-right-radius: 0.475rem !important;
  border-top-left-radius: 0.475rem !important;
}
.custom-search .wrapper div:nth-child(2) .selected:last-child {
  border-bottom-right-radius: 0.475rem !important;
  border-bottom-left-radius: 0.475rem !important;
}
.custom-search .wrapper div:nth-child(2) .selected:before,
.custom-search .wrapper div:nth-child(2) .selected .ellipsis span {
  color: #ffffff !important;
}
.custom-search input {
  display: block !important;
  width: 100% !important;
  padding: 0.688rem 0.938rem !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #6c757d !important;
  background-color: #ffffff !important;
  background-clip: padding-box !important;
  border: 1px solid #ced4da !important;
  appearance: none !important;
  padding-right: 42px !important;
  border-radius: 0.475rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
  font-family: Poppins, Helvetica, "sans-serif" !important;
}
.custom-search input::placeholder {
  color: #6c757d !important;
}

.react-search-icon {
  right: 13px !important;
  top: 15px !important;
  z-index: 99 !important;
  left: auto !important;
}

.sweet-alert {
  will-change: unset !important;
  border-radius: 0.625rem !important;
  background-color: #ffffff !important;
  width: 375px !important;
}
.sweet-alert h2 {
  color: #212529 !important;
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  margin-bottom: 13px !important;
}
.sweet-alert .sweet-text {
  font-size: 0.875rem !important;
  color: #6c757d !important;
}
.sweet-alert p {
  flex-direction: row-reverse;
}

.Toastify {
  z-index: 999999;
}
.Toastify__toast {
  box-shadow: 0 1rem 2rem 1rem rgba(173, 181, 189, 0.2);
  border-radius: 10px;
  min-height: 80px;
}
.Toastify__toast-container {
  width: auto;
  opacity: 1;
}

.toast-card {
  font-family: Poppins, Helvetica, "sans-serif";
  margin-left: 30px !important;
}
.toast-card__icon--success {
  color: #ffffff;
  background-color: #0ac074;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.toast-card__icon--error {
  color: #ffffff;
  background-color: #f62947;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.toast-card__icon svg {
  padding: 5px;
  font-size: 10px;
}
.toast-card__toast-title {
  color: #212529;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 0 !important;
}
.toast-card__toast-message {
  color: #495057;
  font-size: 14px;
  margin-bottom: 0 !important;
}
.toast-card__close-btn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 20px;
  cursor: pointer;
  color: #495057;
  display: flex;
}

.Toastify__toast-container--top-right {
  left: 0 !important;
}

@media (max-width: 480px) {
  .Toastify__toast-container--top-right {
    min-width: 260px;
    max-width: 260px;
    top: 1em !important;
    left: 0;
    right: auto;
  }
}

.Toastify__toast--rtl {
  direction: rtl;
}

.upload-file {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  right: 0;
  left: 0;
  margin: auto;
}

.contents {
  background: #eef0f8 !important;
}

#root {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.toolbar {
  padding: 0 !important;
}

.btn-pink {
  color: #ff679b;
}

.accordion-button::after {
  background-size: 10px;
  opacity: 0.5;
}

.modal-content .modal-header .close {
  border: none;
  font-size: 23px;
  background: none;
}

.object-fit-contain {
  object-fit: contain !important;
}

.object-fit-cover {
  object-fit: cover;
}

.brand-image {
  height: 140px;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.user_avatar {
  align-items: center;
  background: #0015ff;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  width: 80px;
  height: 80px;
}
.user_avatar span {
  font-size: 15px;
}

.custom-user-avatar {
  align-items: center;
  background: #0015ff;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  width: 50px;
  height: 50px;
  justify-content: center;
  font-size: 25px;
}

.image-input img {
  object-fit: contain;
}

.form-check-input:focus {
  outline: none !important;
  border-color: #ebedf3;
}

.custom-label {
  width: fit-content;
}

.nav-item .nav-link {
  padding: 0 !important;
}

.custom-loading {
  color: #0015ff;
  height: 673px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-loading-detail {
  color: #0015ff;
  height: 565px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-company-logo {
  width: 68px !important;
}
@media (max-width: 575px) {
  .login-company-logo {
    max-width: 68px !important;
  }
}

.custom-img {
  max-width: 40px;
  object-fit: contain;
}

.image > img {
  flex-shrink: 0;
  display: inline-block;
  object-fit: cover;
}

.custom-card-header {
  min-height: unset !important;
  padding: unset !important;
}

.custom-cash-card {
  border: 0 !important;
  border-radius: 10px !important;
  box-shadow: 0 4px 20px 1px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.08) !important;
}

.react-datepicker-popper {
  z-index: 999 !important;
}

.custom-line-height {
  line-height: 1.6;
  letter-spacing: 0.3px;
}

.form-group .input-group .form-control-solid {
  border-radius: 0.475rem !important;
}
.form-group .input-group .input-group-text {
  align-items: center !important;
  background-color: #e9ecef !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.313rem 0 0 0.313rem !important;
  color: #6c757d !important;
  display: flex !important;
  font-size: 0.875rem !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  padding: 0.688rem 0.938rem !important;
  text-align: center !important;
  white-space: nowrap !important;
}

.custom-overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 1;
}

@media (min-width: 1200px) {
  .wrapper-res {
    transition: padding-right 0.3s ease;
    padding-right: 4.563rem;
  }
}

.required:after {
  font-weight: 400 !important;
}

.custom-qty {
  max-width: 140px;
  /* Firefox */
}
.custom-qty input[type=number] {
  width: 35px;
}
.custom-qty input::-webkit-outer-spin-button,
.custom-qty input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.custom-qty input[type=number] {
  -moz-appearance: textfield;
}

.table > :not(caption) > * > * {
  vertical-align: middle !important;
}
@media (max-width: 925px) {
  .table > :not(caption) > * > * {
    padding: 0.5rem 0.313rem !important;
  }
}

.logo-height {
  object-fit: contain;
}

.email-template-padding {
  padding-right: 100px !important;
}
@media (max-width: 768px) {
  .email-template-padding {
    padding-right: 0px !important;
  }
}

.custom-text-center {
  text-align: center !important;
}

.profit-loss {
  padding: 20px 10px;
  background: white;
  margin-top: -1pc;
  border-radius: 0px 0px 10px 10px;
}

.sms_api .sms-api-main {
  background-color: #0015ff;
  color: #ffffff !important;
  padding: 2.5rem 2.5rem;
}
.sms_api .sms-api-main ul {
  padding-right: 41px;
}
.sms_api .sms-api-main ul li {
  list-style: disc;
}
.sms_api .sms-api-main h1 {
  color: #ffffff !important;
}
@media (max-width: 575px) {
  .sms_api .sms-api-main {
    padding: 0.5rem 0.5rem !important;
  }
}

.w-100px {
  width: 100px;
}

.w-117px {
  width: 117px;
}

.form-control[readonly] {
  background-color: hsl(0, 0%, 95%) !important;
}

.model-dtn {
  top: 29px;
  left: 1px;
  border-radius: 0.475rem 0 0 0.475rem !important;
  height: -webkit-fill-available;
  max-height: 45px;
}

@keyframes load {
  from {
    right: -150px;
  }
  to {
    right: 100%;
  }
}
.maindata {
  width: 100%;
  padding: 25px;
}

.rowdata {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: space-between;
  align-items: center;
}

.dark-theme .skeleton {
  background-color: #3a3a4f !important;
}
.dark-theme .skeleton::before {
  background: linear-gradient(to left, transparent 0%, #7c7f87 50%, transparent 100%);
}
.dark-theme .animated-background {
  background: linear-gradient(to left, #3a3a4f 8%, #7c7f87 18%, #3a3a4f 33%);
}
.dark-theme .rdt_Table > :nth-child(1) {
  background-color: #212125 !important;
}

.skeleton {
  background: #e1e1e1;
  height: 133px;
  position: relative;
  overflow: hidden;
  width: 15%;
  margin: 1% 0%;
}
.skeleton::before {
  content: "";
  display: block;
  position: absolute;
  right: -150px;
  top: 0;
  height: 100%;
  width: 150px;
  background: linear-gradient(to left, transparent 0%, #e8e8e8 50%, transparent 100%);
  animation: load 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes placeHolderShimmer {
  0% {
    background-position: right -800px top 0;
  }
  100% {
    background-position: right 800px top 0;
  }
}
.animated-background {
  animation-duration: 2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeHolderShimmer;
  animation-timing-function: linear;
  background-color: #f6f7f8;
  background: linear-gradient(to left, #eeeeee 8%, #bbbbbb 18%, #eeeeee 33%);
  background-size: 800px 104px;
  height: 50px !important;
  width: 100%;
  position: relative;
}

.background-masker {
  background-color: #fff;
  position: absolute;
}

.showpassword {
  display: flex;
  align-items: center;
  padding: 0.688rem 0.938rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #6c757d;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.313rem;
  cursor: pointer;
}

.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.payment_settings input[type=checkbox] {
  cursor: pointer;
}

.setting-tab .nav-item .nav-link {
  border-right: 5px solid transparent;
  border-radius: 0 !important;
  padding: 10px 10px 10px 20px !important;
  transition: 0.3s all ease-in-out;
  color: #6c757d;
  font-size: 16px;
  margin-bottom: -1px;
}
.setting-tab .nav-item .nav-link:hover, .setting-tab .nav-item .nav-link.active {
  color: #212529;
  border-right: 5px solid #6571ff !important;
  background: #e0e3ff !important;
}

.pricing-card {
  border-radius: 20px !important;
}

.ml-20px {
  margin-right: 20px !important;
}

.ql-toolbar.ql-snow + .ql-container.ql-snow {
  height: 100px;
}

.ellipsis {
  text-overflow: ellipsis;
  width: 180px;
}

.pages-quill .ql-toolbar.ql-snow + .ql-container.ql-snow {
  height: 250px;
}

.btn-purple {
  border-color: #8635fd !important;
  background-color: #8635fd !important;
  color: white !important;
}
.btn-purple:hover {
  border-color: #8635fd !important;
  background-color: #7a28f3 !important;
}

.text-purple {
  color: #8635fd !important;
}

.languague-dropdown .dropdown-menu {
  min-width: 200px !important;
}

.store-dropdown .dropdown-menu {
  transform: none !important;
  top: 60px !important;
  left: auto !important;
  min-width: 220px !important;
  border: 1px solid #eeeeee !important;
  border-radius: 5px !important;
  padding: 10px 0 !important;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px !important;
  max-height: 200px;
  overflow: auto;
}

.margin-top {
  margin-top: 70px !important;
}

@media (max-width: 480px) {
  .filter-dropdown .dropdown-menu {
    right: 0 !important;
  }
}

@media (max-width: 480px) {
  .table-button {
    padding: 10px 7px !important;
  }
}

.store-dropdown .btn {
  padding: 0.563rem 0 !important;
  width: 175px !important;
  height: 60px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.store-name {
  width: 110px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout {
  overflow: visible !important;
}

.plan-warning {
  box-shadow: rgba(95, 95, 95, 0.23) 0px 0px 8px !important;
  border-radius: 15px !important;
  margin: 15px 0 40px 0px !important;
  padding: 15px 20px !important;
}

.tooltip.custom-light .tooltip-inner {
  background-color: #ffffff;
  color: #212529;
  border: 1px solid #dee2e6;
  padding: 0;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  width: 200px !important;
}

.store-tooltip-item {
  max-height: 150px !important;
  overflow: auto !important;
}

.audio-preview {
  height: 45px !important;
}

.pos-audio-button {
  min-width: max-content !important;
}

.search-image {
  width: 40px !important;
  height: 40px !important;
  border-radius: 4px !important;
  object-fit: cover !important;
}

.customer-display-table {
  max-height: 475px !important;
  overflow: auto !important;
}

.customer-display-carousel::after {
  background-color: rgba(33, 37, 41, 0) !important;
}

.carousel-img {
  height: 600px !important;
}

.product-notes {
  min-height: 125px !important;
}

.dropdown-menu {
  position: absolute !important;
  padding: 1rem 0 !important;
  top: 0 !important;
  box-shadow: 0 5px 20px rgba(173, 181, 189, 0.2);
  transition: all 400ms ease-in-out;
  left: 0 !important;
  right: auto !important;
}
.dropdown-menu .dropdown-item:active {
  background-color: #f8f9fa;
  color: #0015ff;
}

.separator {
  border-bottom: 1px solid #eff2f5 !important;
  display: block;
  height: 0;
}

@media (max-width: 400px) {
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    min-width: 230px !important;
    width: 100% !important;
    right: unset !important;
  }
}

.menu-icon-grid button {
  align-items: center;
  color: #212529;
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.language-btn:hover {
  background-color: #0015ff !important;
  color: #ffffff !important;
}
.language-btn:focus {
  border-color: #ffffff !important;
}
.language-btn.language-btn-active {
  background-color: #0015ff !important;
  border-color: #0015ff !important;
}
.language-btn.language-btn-active span {
  color: #ffffff !important;
}
.language-btn img {
  max-width: 20px;
}

.hide-arrow .dropdown-toggle:after {
  content: none !important;
}

.pos-button {
  border: 1px solid #0015ff;
  color: whitesmoke;
  padding: 10px 20px;
  border-radius: 50%;
  text-decoration: none;
  transition-duration: 0.4s;
  cursor: pointer;
  background-color: #0015ff;
  transition: all 0.6s ease-in-out;
  box-shadow: 0 0 17px 1px rgba(173, 181, 189, 0.05), 0 6px 20px 0 rgba(0, 0, 0, 0.15);
}

.pos-button-highlight:hover {
  background-color: transparent;
  color: #0015ff;
}

@keyframes swing {
  0%, 30%, 50%, 70%, 100% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  40% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(-5deg);
  }
  80% {
    transform: rotate(5deg);
  }
}
.pro-sidebar {
  color: #adadad;
  height: 100%;
  width: 270px;
  min-width: 270px;
  text-align: right;
  transition: width, right, left, 0.3s;
  position: relative;
  z-index: 1009;
}
.pro-sidebar > .pro-sidebar-inner {
  background: #1d1d1d;
  height: 100%;
  position: relative;
  z-index: 101;
}
.pro-sidebar > .pro-sidebar-inner > img.sidebar-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  position: absolute;
  opacity: 0.3;
  right: 0;
  top: 0;
  z-index: 100;
}
.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 101;
}
.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout .pro-sidebar-header {
  border-bottom: 1px solid rgba(173, 173, 173, 0.2);
}
.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout .pro-sidebar-content {
  flex-grow: 1;
}
.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout .pro-sidebar-footer {
  border-top: 1px solid rgba(173, 173, 173, 0.2);
}
.pro-sidebar > .pro-sidebar-inner > .pro-sidebar-layout ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.pro-sidebar .overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 100;
  display: none;
}
.pro-sidebar.collapsed {
  width: 80px;
  min-width: 80px;
}
.pro-sidebar.rtl {
  text-align: left;
  direction: ltr;
}
@media (max-width: 480px) {
  .pro-sidebar.xs {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.xs.collapsed {
    right: -80px;
  }
  .pro-sidebar.xs.toggled {
    right: 0;
  }
  .pro-sidebar.xs.toggled .overlay {
    display: block;
  }
  .pro-sidebar.xs.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.xs.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.xs.rtl.toggled {
    right: auto;
    left: 0;
  }
}
@media (max-width: 576px) {
  .pro-sidebar.sm {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.sm.collapsed {
    right: -80px;
  }
  .pro-sidebar.sm.toggled {
    right: 0;
  }
  .pro-sidebar.sm.toggled .overlay {
    display: block;
  }
  .pro-sidebar.sm.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.sm.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.sm.rtl.toggled {
    right: auto;
    left: 0;
  }
}
@media (max-width: 768px) {
  .pro-sidebar.md {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.md.collapsed {
    right: -80px;
  }
  .pro-sidebar.md.toggled {
    right: 0;
  }
  .pro-sidebar.md.toggled .overlay {
    display: block;
  }
  .pro-sidebar.md.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.md.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.md.rtl.toggled {
    right: auto;
    left: 0;
  }
}
@media (max-width: 992px) {
  .pro-sidebar.lg {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.lg.collapsed {
    right: -80px;
  }
  .pro-sidebar.lg.toggled {
    right: 0;
  }
  .pro-sidebar.lg.toggled .overlay {
    display: block;
  }
  .pro-sidebar.lg.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.lg.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.lg.rtl.toggled {
    right: auto;
    left: 0;
  }
}
@media (max-width: 1200px) {
  .pro-sidebar.xl {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.xl.collapsed {
    right: -80px;
  }
  .pro-sidebar.xl.toggled {
    right: 0;
  }
  .pro-sidebar.xl.toggled .overlay {
    display: block;
  }
  .pro-sidebar.xl.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.xl.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.xl.rtl.toggled {
    right: auto;
    left: 0;
  }
}
@media (max-width: 1600px) {
  .pro-sidebar.xxl {
    position: fixed;
    right: -270px;
  }
  .pro-sidebar.xxl.collapsed {
    right: -80px;
  }
  .pro-sidebar.xxl.toggled {
    right: 0;
  }
  .pro-sidebar.xxl.toggled .overlay {
    display: block;
  }
  .pro-sidebar.xxl.rtl {
    right: auto;
    left: -270px;
  }
  .pro-sidebar.xxl.rtl.collapsed {
    right: auto;
    left: -80px;
  }
  .pro-sidebar.xxl.rtl.toggled {
    right: auto;
    left: 0;
  }
}

.pro-sidebar .pro-menu.submenu-bullets .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item:before {
  content: "";
  display: inline-block;
  width: 4px;
  min-width: 4px;
  height: 4px;
  border: 1px solid #2b2b2b;
  border-radius: 50%;
  margin-left: 15px;
  position: relative;
  box-shadow: -1px 0px 0px #adadad, 0px -1px 0px #adadad, 0px 1px 0px #adadad, 1px 0px 0px #adadad;
}

.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item > .pro-item-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pro-sidebar .pro-menu {
  padding-top: 10px;
  padding-bottom: 10px;
}
.pro-sidebar .pro-menu > ul > .pro-sub-menu > .pro-inner-list-item {
  position: relative;
  background-color: #2b2b2b;
}
.pro-sidebar .pro-menu > ul > .pro-sub-menu > .pro-inner-list-item > div > ul {
  padding-top: 15px;
  padding-bottom: 15px;
}
.pro-sidebar .pro-menu a {
  text-decoration: none;
  color: #adadad;
}
.pro-sidebar .pro-menu a:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: transparent;
}
.pro-sidebar .pro-menu a:hover {
  color: #d8d8d8;
}
.pro-sidebar .pro-menu .pro-menu-item {
  font-size: 15px;
}
.pro-sidebar .pro-menu .pro-menu-item.active {
  color: #d8d8d8;
}
.pro-sidebar .pro-menu .pro-menu-item .suffix-wrapper {
  opacity: 1;
  transition: opacity 0.2s;
}
.pro-sidebar .pro-menu .pro-menu-item .prefix-wrapper {
  display: flex;
  margin-left: 5px;
  opacity: 1;
  transition: opacity 0.2s;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 20px 8px 35px;
  cursor: pointer;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item:focus {
  outline: none;
  color: #d8d8d8;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  margin-left: 10px;
  font-size: 14px;
  width: 35px;
  min-width: 35px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  display: inline-block;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item > .pro-icon-wrapper .pro-icon {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item > .pro-item-content {
  flex-grow: 1;
  flex-shrink: 1;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item:hover {
  color: #d8d8d8;
}
.pro-sidebar .pro-menu .pro-menu-item > .pro-inner-item:hover .pro-icon-wrapper .pro-icon {
  animation: swing ease-in-out 0.5s 1 alternate;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu > .pro-inner-item:before {
  background: #adadad;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu > .pro-inner-item > .pro-arrow-wrapper {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu > .pro-inner-item > .pro-arrow-wrapper .pro-arrow {
  display: inline-block;
  border-style: solid;
  border-color: #adadad;
  border-width: 0 0 2px 2px;
  padding: 2.5px;
  vertical-align: middle;
  transition: transform 0.3s;
  transform: rotate(45deg);
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu.open > .pro-inner-item:before {
  background: transparent !important;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu.open > .pro-inner-item > .pro-arrow-wrapper .pro-arrow {
  transform: rotate(-45deg);
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item {
  padding-right: 24px;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item {
  padding: 8px 15px 8px 30px;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item > .pro-icon-wrapper {
  background: none;
  width: auto;
  min-width: auto;
  height: auto;
  line-height: auto;
}
.pro-sidebar .pro-menu:not(.inner-submenu-arrows) .pro-inner-list-item .pro-menu-item.pro-sub-menu .pro-inner-item .pro-arrow-wrapper {
  display: none;
}
.pro-sidebar .pro-menu.shaped .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  background-color: #2b2b2b;
}
.pro-sidebar .pro-menu.square .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  border-radius: 0;
}
.pro-sidebar .pro-menu.round .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  border-radius: 4px;
}
.pro-sidebar .pro-menu.circle .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  border-radius: 50%;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item {
  position: relative;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item > .pro-inner-item > .suffix-wrapper,
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item > .pro-inner-item > .prefix-wrapper {
  opacity: 0;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item > .pro-inner-list-item {
  background-color: #2b2b2b;
  z-index: 111;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item::before {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  cursor: pointer;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu {
  position: relative;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-item {
  pointer-events: none;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-item > .pro-arrow-wrapper {
  display: none;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item {
  height: auto !important;
  position: fixed;
  visibility: hidden;
  min-width: 220px;
  max-width: 270px;
  background-color: transparent;
  max-height: 100%;
  padding-right: 3px;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item.has-arrow {
  padding-right: 10px;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item > .popper-inner {
  max-height: 100vh;
  overflow-y: auto;
  background-color: #2b2b2b;
  padding-right: 20px;
  border-radius: 4px;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu:hover > .pro-inner-list-item {
  transition: visibility, transform 0.3s;
  visibility: visible;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu:hover .pro-icon-wrapper .pro-icon {
  animation: swing ease-in-out 0.5s 1 alternate;
}
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-sub-menu-item,
.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item {
  padding: 8px 5px 8px 30px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item .prefix-wrapper {
  margin-left: 0;
  margin-right: 5px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item > .pro-inner-item {
  padding: 8px 35px 8px 20px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item > .pro-inner-item > .pro-icon-wrapper {
  margin-left: 0;
  margin-right: 10px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu > .pro-inner-item > .pro-arrow-wrapper {
  left: auto;
  right: 20px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu > .pro-inner-item > .pro-arrow-wrapper .pro-arrow {
  transform: rotate(-135deg);
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu.open > .pro-inner-item > .pro-arrow-wrapper .pro-arrow {
  transform: rotate(-45deg);
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item {
  padding-right: 0;
  padding-left: 24px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item {
  padding: 8px 30px 8px 15px;
}
.pro-sidebar.rtl .pro-menu .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item:before {
  margin-left: 0;
  margin-right: 15px;
}
.pro-sidebar.rtl.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item {
  padding-right: 0;
  padding-left: 3px;
}
.pro-sidebar.rtl.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item.has-arrow {
  padding-left: 10px;
}
.pro-sidebar.rtl.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu > .pro-inner-list-item > .popper-inner {
  padding-right: 0;
  padding-left: 20px;
}
.pro-sidebar.rtl.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-sub-menu-item,
.pro-sidebar.rtl.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu .pro-inner-list-item .pro-inner-item {
  padding: 8px 30px 8px 5px;
}

.popper-arrow {
  position: absolute;
  z-index: -1;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

.popper-element[data-popper-placement^=left] > .popper-arrow {
  left: 0;
  border-left: 7px solid #2b2b2b;
}

.popper-element[data-popper-placement^=right] > .popper-arrow {
  right: 0;
  border-right: 7px solid #2b2b2b;
}

.react-slidedown {
  height: 0;
  transition-property: none;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
}

.react-slidedown.transitioning {
  overflow-y: hidden;
}

.react-slidedown.closed {
  display: none;
}

@media (min-width: 992px) {
  .pro-sidebar {
    min-height: 100vh;
    height: auto;
  }
}
@media (max-width: 992px) {
  .pro-sidebar {
    position: fixed;
    transition: 0.3s ease-in-out;
    right: -270px;
    overflow: auto;
  }
}
.pro-sidebar .pro-sidebar-inner {
  background-color: #ffffff !important;
}
@media (max-width: 992px) {
  .pro-sidebar .pro-sidebar-inner {
    min-height: 100vh;
    height: auto;
  }
}
.pro-sidebar .pro-sidebar-inner .pro-sidebar-layout {
  overflow-y: hidden;
}
.pro-sidebar .pro-menu {
  padding: 0px !important;
}
.pro-sidebar .pro-menu.sidebar-scrolling {
  height: calc(100% - 70px);
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-item {
  border-right: 0.313rem solid transparent;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  padding: 5px 18px 5px 20px;
  font-size: 0.875rem;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-item:hover {
  background-color: #e0e3ff;
  border-right: 0.313rem solid #0015ff !important;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-item:hover svg,
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-item:hover .pro-item-content a {
  color: #212529;
}
.pro-sidebar .pro-menu .pro-menu-item.active .pro-inner-item {
  background-color: #e0e3ff;
  border-right: 0.313rem solid #0015ff !important;
}
.pro-sidebar .pro-menu .pro-menu-item.active .pro-inner-item svg,
.pro-sidebar .pro-menu .pro-menu-item.active .pro-inner-item .pro-item-content a {
  color: #212529;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-icon-wrapper {
  margin-left: 4px !important;
  margin-right: 0 !important;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-icon-wrapper .pro-icon {
  color: #6c757d;
  font-size: 0.875rem !important;
  animation: unset !important;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-item-content a {
  color: #6c757d;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-list-item {
  position: relative;
  background-color: transparent !important;
  padding: 0px !important;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-list-item ul {
  padding: 0px !important;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-list-item .pro-inner-item {
  padding: 10px 43px 10px 43px !important;
}
.pro-sidebar .pro-menu .pro-menu-item span.pro-item-content {
  color: #6c757d;
}
.pro-sidebar .pro-menu .pro-menu-item .pro-inner-list-item ul span.pro-item-content {
  padding-right: 10px !important;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu.pro-active-sub > div > .pro-item-content {
  color: #0015ff !important;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu.pro-active-sub > div.pro-inner-item {
  border-right: 0.313rem solid #0015ff !important;
}
.pro-sidebar .pro-menu .pro-menu-item.pro-sub-menu.pro-active-sub-search > div {
  background-color: #e0e3ff;
}

.pro-sidebar.collapsed .pro-menu > ul > .pro-menu-item.pro-sub-menu:hover .react-slidedown.pro-inner-list-item {
  transition: visibility, transform 0.3s;
  position: fixed;
  right: 82px;
  margin: 0px;
  display: block !important;
  color: #6c757d;
  background-color: white !important;
  border-radius: 5px;
  margin-top: -44px;
  box-shadow: 0 4px 20px 1px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.08) !important;
}

.react-slidedown {
  transition-duration: 0.3s !important;
}

.notShow {
  display: none;
}

.openMenu {
  display: block !important;
  height: auto !important;
  transition-duration: 0.5s !important;
}

.closeMenu {
  display: none !important;
  height: 0px !important;
  transition-duration: 0.5s !important;
}

@media (max-width: 1199px) {
  .open-menu {
    right: 0 !important;
  }
}

@media (max-width: 992px) {
  .bg-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: 109;
    background-color: rgba(0, 0, 0, 0.2);
    animation: animation-drawer-fade-in 0.3s ease-in-out 1;
    display: none;
  }
}

.header-active {
  border-bottom: 2px solid #0015ff;
  color: #060917 !important;
}

.header-tabs:hover,
.header-tabs:focus {
  color: #060917 !important;
  border-bottom: 2px solid #0015ff;
}

.word-break-all {
  word-break: break-all;
}

.barcode-main {
  border: 1px solid #ccc;
  display: block;
  page-break-after: always;
  padding: 0.1in;
  width: 8.25in;
}
.barcode-main__barcode-item {
  border: 1px dotted #ccc;
  display: block;
  float: right;
  font-size: 12px;
  line-height: 14px;
  overflow: hidden;
  text-align: center;
  text-transform: uppercase;
  page-break-inside: avoid;
  break-inside: avoid; /* For modern browsers */
}
.barcode-main__barcode-style {
  padding: 0.04in;
  width: 1.799in;
}
.barcode-main__barcode-style img {
  height: 26px;
  max-width: 80px;
}

.print-main {
  height: 110px;
  padding: 0.12in;
}
.print-main__print1 {
  width: 25%;
  flex: 0 0 auto;
}
.print-main__print2 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.print-main__print3 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.print-main__print4 {
  flex: 0 0 auto;
  width: 50%;
}
.print-main__print5 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.print-main__print7 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.print-main__print8 {
  flex: 0 0 auto;
  width: 50%;
}

.shortcut-btn {
  font-size: 20px;
  border-radius: 20% !important;
}

.shortcut-menu {
  right: 0 !important;
  top: 15px !important;
  min-width: 225px !important;
}
.shortcut-menu a {
  font-size: 15px !important;
}

.chart-dropdown .dropdown-menu {
  min-width: 87px !important;
  transform: unset !important;
  right: auto !important;
  left: 16px !important;
  top: 0 !important;
  padding: 10px !important;
}

.pie-chart {
  height: auto !important;
  width: 320px !important;
}

.widget-bg-orange {
  background-color: orange;
  opacity: 1;
}

.bg-orange-700 {
  background-color: rgba(247, 212, 147, 0.63) !important;
}

.widget-bg-purple {
  background-color: #6f42c1;
  opacity: 1;
}

.bg-purple-700 {
  background-color: #bda4ea !important;
}

.widget-bg-pink {
  background-color: #e83e8c;
  opacity: 1;
}

.bg-pink-700 {
  background-color: #f4a9cb !important;
}

.widget-bg-red {
  background-color: red;
  opacity: 1;
}

.bg-red-700 {
  background-color: #f4a9cb !important;
}

.widget-bg-blue {
  background-color: #00c6ff;
  opacity: 1;
}

.widget-bg-blue-700 {
  background-color: #a9ecff !important;
}

.uploadInput {
  background-color: #fff;
  border: 1px dashed #d6d6d6;
  border-radius: 4px;
  height: 180px;
  width: 190px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-image {
  width: 100%;
  height: auto;
  max-width: 100px;
}

.upload-text {
  color: #777;
  font-weight: 400;
  line-height: 1.5;
  padding-top: 5px;
  font-size: 12px;
}

.add-image {
  border: 1px solid #d6d6d6;
  border-radius: 4px;
  height: 32px;
  width: 32px;
}
.add-image i {
  font-size: 24px;
  color: #000000;
}

.remove-btn.btn.btn-light:hover:not(.btn-active) {
  background-color: transparent !important;
}

.previewItem {
  border: 1px solid #ddd;
  height: 100px;
  width: calc(25% - 10px);
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  border-radius: 4px;
}
.previewItem .remove-btn {
  background-color: #6571ff !important;
  width: 25px;
  height: 25px;
  position: absolute;
  left: -5px;
  border-radius: 50px;
  top: -10px;
  opacity: 0;
  transition: all 0.3s;
}
.previewItem .remove-btn i {
  margin-right: 4px;
  font-size: 12px;
}
.previewItem:hover .remove-btn {
  opacity: 1;
}

.imagePreview {
  width: 100%;
  max-width: 100px;
  height: 80px;
  margin: auto;
  display: block;
  object-fit: contain;
}

.product-details-img {
  object-fit: contain;
}

.slick-slider {
  width: 413px;
  display: flex !important;
  justify-content: center;
  align-items: center;
}
@media (max-width: 576px) {
  .slick-slider {
    width: 100%;
  }
}

.slick-arrow {
  background: unset !important;
}

.slick-prev {
  right: -45px !important;
}
@media (max-width: 576px) {
  .slick-prev {
    right: -30px !important;
  }
}

.slick-prev:before {
  content: "\f284" !important;
  font-family: "bootstrap-icons" !important;
  color: gray !important;
  font-size: 35px !important;
}
@media (max-width: 576px) {
  .slick-prev:before {
    font-size: 25px !important;
  }
}

.slick-next:before {
  content: "\f285" !important;
  font-family: "bootstrap-icons" !important;
  color: gray !important;
  font-size: 35px !important;
}
@media (max-width: 576px) {
  .slick-next:before {
    font-size: 25px !important;
  }
}

.rec-carousel-item {
  display: flex;
  align-items: center;
}

.rec-pagination {
  display: none !important;
}

.rec-arrow:hover:enabled,
.rec-arrow:focus:enabled {
  color: #fff;
  background-color: rgb(57, 153, 255) !important;
  box-shadow: 0 0 2px 0 #333;
}

.rec-arrow {
  background-color: rgba(54, 153, 255, 0.1) !important;
}

.ReactModal__Overlay--after-open {
  z-index: 9999 !important;
}
.ReactModal__Body--open {
  overflow: hidden;
}

.product_brcode {
  width: 200px;
  height: 80px;
}

.main-product-details tbody tr:last-child td {
  border-bottom: 1px solid #dee2e6;
}