import React from "react";
import { connect } from "react-redux";
import DeleteModel from "../../shared/action-buttons/DeleteModel";
import { deleteSale } from "../../store/action/salesAction";
import { getFormattedMessage } from "../../shared/sharedMethod";

const DeleteOrder = (props) => {
    const { deleteSale, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteSaleClick = () => {
        deleteSale(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && (
                <DeleteModel
                    onClickDeleteModel={onClickDeleteModel}
                    deleteModel={deleteModel}
                    deleteClick={deleteSaleClick}
                    name={getFormattedMessage("order.title")}
                />
            )}
        </div>
    );
};

export default connect(null, { deleteSale })(DeleteOrder);
