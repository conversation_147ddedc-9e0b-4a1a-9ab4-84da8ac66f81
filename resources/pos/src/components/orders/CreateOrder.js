import React, { useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getFormattedMessage } from "../../shared/sharedMethod";
import { fetchAllCustomer } from "../../store/action/customerAction";
import { addSale } from "../../store/action/salesAction";
import { fetchAllWarehouses } from "../../store/action/warehouseAction";
import MasterLayout from "../MasterLayout";
import HeaderTitle from "../header/HeaderTitle";
import OrderForm from "./OrderForm";
import { ROUTES } from "../../constants";

const CreateOrder = (props) => {
    const {
        addSale,
        customers,
        fetchAllCustomer,
        warehouses,
        fetchAllWarehouses,
    } = props;
    const navigate = useNavigate();

    useEffect(() => {
        fetchAllCustomer();
        fetchAllWarehouses();
    }, []);

    const addSaleData = (formValue) => {
        addSale(formValue, navigate);
    };

    return (
        <MasterLayout>
            <HeaderTitle
                title={getFormattedMessage("order.create.title")}
                to={ROUTES.ORDERS}
            />
            <OrderForm
                addSaleData={addSaleData}
                customers={customers}
                warehouses={warehouses}
            />
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const { customers, warehouses, totalRecord } = state;
    return { customers, warehouses, totalRecord };
};

export default connect(mapStateToProps, {
    addSale,
    fetchAllCustomer,
    fetchAllWarehouses,
})(CreateOrder);
