<?php

namespace App\Models;

use App\Traits\Multitenantable;
use App\Traits\HasJsonResourcefulData;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AddOn extends BaseModel
{
    use HasFactory, BelongsToTenant, HasJsonResourcefulData,Multitenantable;

    protected $table = 'add_ons';

    protected $fillable = [
        'name',
        'price'
    ];

    public function getIdFilterFields(): array
    {
        return [
            'id' => self::class,
        ];
    }

    public static function rules(): array
    {
        return [
            'name' => 'required',
            'price' => 'required',
        ];
    }

    public function prepareLinks(): array
    {
        return [
            'self' => route('add-ons.show', $this->id),
        ];
    }

    public function prepareAttributes(): array
    {
        $fields = [
            'name' => $this->name,
            'price' => $this->price,
        ];

        return $fields;
    }
}
