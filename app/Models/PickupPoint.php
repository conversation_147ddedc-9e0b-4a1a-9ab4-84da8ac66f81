<?php

namespace App\Models;

use App\Traits\Multitenantable;
use App\Traits\HasJsonResourcefulData;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PickupPoint extends BaseModel
{
    use HasFactory, BelongsToTenant, HasJsonResourcefulData,Multitenantable;

    protected $table = 'pickup_points';

    protected $fillable = [
        'name',
        'tenant_id',
        'person_name',
        'address',
        'phone',
    ];

    public function getIdFilterFields(): array
    {
        return [
            'id' => self::class,
        ];
    }

    public static function rules(): array
    {
        return [
            'name' => 'required',
            'person_name' => 'required',
            'address' => 'required',
            'phone' => 'required|numeric',
        ];
    }

    public function prepareLinks(): array
    {
        return [
            'self' => route('pickup-points.show', $this->id),
        ];
    }

    public function prepareAttributes(): array
    {
        $fields = [
            'name' => $this->name,
            'person_name' => $this->person_name,
            'address' => $this->address,
            'phone' => $this->phone,
        ];

        return $fields;
    }
}
