<?php

namespace App\Models;

use App\Traits\Multitenantable;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasJsonResourcefulData;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Table extends BaseModel
{
    use HasFactory, BelongsToTenant, HasJsonResourcefulData,Multitenantable;

    protected $table = 'tables';

    protected $fillable = [
        'table_name',
        'table_code',
        'max_seat_capacity',
        'tenant_id',
        'status',
        'table_category_id',
    ];

    public function getIdFilterFields(): array
    {
        return [
            'id' => self::class,
        ];
    }

    public static function rules(): array
    {
        return [
            'table_name' => 'required',
            'max_seat_capacity' => 'required',
            'status' => 'required',
            'table_category_id' => 'required',
        ];
    }

    public function prepareLinks(): array
    {
        return [
            'self' => route('tables.show', $this->id),
        ];
    }

    public function prepareAttributes(): array
    {
        $fields = [
            'table_name' => $this->table_name,
            'table_code' => $this->table_code,
            'max_seat_capacity' => $this->max_seat_capacity,
            'status' => $this->status,
            'table_category_id' => $this->table_category_id,
        ];

        return $fields;
    }

    public function tableCategory()
    {
        return $this->belongsTo(TableCategory::class);
    }
}
