<?php

namespace App\Models;

use App\Traits\Multitenantable;
use App\Traits\HasJsonResourcefulData;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class TableCategory extends BaseModel
{
    use HasFactory, BelongsToTenant, HasJsonResourcefulData, Multitenantable;

    protected $table = 'table_categories';

    protected $fillable = [
        'name',
        'tenant_id',
        'status',
    ];

    public function getIdFilterFields(): array
    {
        return [
            'id' => self::class,
        ];
    }

    public static function rules(): array
    {
        return [
            'name' => 'required',
        ];
    }

    public function prepareLinks(): array
    {
        return [
            'self' => route('table-categories.show', $this->id),
        ];
    }

    public function prepareAttributes(): array
    {
        $fields = [
            'name' => $this->name,
            'status' => (bool) $this->status,
        ];

        return $fields;
    }

    public function table()
    {
        return $this->hasMany(Table::class);
    }
}
