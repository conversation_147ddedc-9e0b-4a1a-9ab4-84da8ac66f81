<?php

namespace App\Http\Controllers\API\M1;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\UpdateChangePasswordRequest;
use App\Http\Requests\UpdateUserProfileRequest;
use App\Models\Language;
use App\Repositories\UserRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserAPIController extends AppBaseController
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function editProfile(): JsonResponse
    {
        $user = Auth::user();
        $userData = [
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'image' => $user->image_url,
        ];

        return $this->sendResponse($userData, 'User data retrieved successfully');
    }

    public function updateProfile(UpdateUserProfileRequest $request)
    {
        $input = $request->all();
        $user = $this->userRepository->updateUserProfile($input);
        $userData = [
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'image' => $user->image_url,
        ];

        return $this->sendResponse($userData, 'User data retrieved successfully');
    }

    public function changePassword(UpdateChangePasswordRequest $request): JsonResponse
    {
        $input = $request->all();
        try {
            $this->userRepository->updatePassword($input);

            return $this->sendSuccess(__('messages.success.password_updated'));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function languages(): JsonResponse
    {
        $languages = Language::get(['id', 'name', 'iso_code', 'is_default']);

        return $this->sendResponse($languages, 'Languages retrieved Successfully');
    }

    public function updateLanguage(Request $request): JsonResponse
    {
        $language = $request->get('language');
        $user = Auth::user();
        $user->update([
            'language' => $language,
        ]);

        return $this->sendResponse($user->language, 'Language Updated Successfully');
    }
}
