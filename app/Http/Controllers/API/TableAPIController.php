<?php

namespace App\Http\Controllers\API;

use App\Models\Table;
use Illuminate\Http\Request;
use App\Models\TableCategory;
use App\Http\Resources\TableResource;
use App\Repositories\TableRepository;
use App\Http\Resources\TableCollection;
use App\Http\Requests\CreateTableRequest;
use App\Http\Requests\UpdateTableRequest;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\TableCategoryResource;
use App\Repositories\TableCategoryRepository;
use App\Http\Resources\TableCategoryCollection;
use App\Http\Requests\CreateTableCategoryRequest;
use App\Http\Requests\UpdateTableCategoryRequest;

class TableAPIController extends AppBaseController
{
    /** @var TableCategoryRepository */
    private $tableRepository;

    public function __construct(TableRepository $tableRepository)
    {
        $this->tableRepository = $tableRepository;
    }

    public function index(): TableCollection
    {
        $tableCategories = $this->tableRepository->paginate(10);
        TableResource::usingWithCollection();
        
        return new TableCollection($tableCategories);
    }

    public function store(CreateTableRequest $request): TableResource
    {
        $input = $request->all();
        $table = $this->tableRepository->storeTable($input);

        TableResource::usingWithCollection();

        return new TableResource($table);
    }

    public function show($id): TableResource
    {
        $tableCategory = Table::findOrFail($id);

        return new TableResource($tableCategory);
    }

    public function update(UpdateTableRequest $request, $id)
    {
        $input = $request->all();

        $brand = $this->tableRepository->updateTable($input, $id);

        return new TableResource($brand);
    }

    public function destroy($id)
    {
        Table::findOrFail($id)->delete();

        return $this->sendSuccess('Table deleted successfully');
    }
}
