<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Models\TableCategory;
use Doctrine\DBAL\Schema\Table;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\TableCategoryResource;
use App\Repositories\TableCategoryRepository;
use App\Http\Resources\TableCategoryCollection;
use App\Http\Requests\CreateTableCategoryRequest;
use App\Http\Requests\UpdateTableCategoryRequest;

class TableCategoryAPIController extends AppBaseController
{
    /** @var TableCategoryRepository */
    private $tableCategoryRepository;

    public function __construct(TableCategoryRepository $tableCategoryRepository)
    {
        $this->tableCategoryRepository = $tableCategoryRepository;
    }

    public function index(): TableCategoryCollection
    {
        $tableCategories = $this->tableCategoryRepository->paginate(10);
        TableCategoryResource::usingWithCollection();
        
        return new TableCategoryCollection($tableCategories);
    }

    public function store(CreateTableCategoryRequest $request): TableCategoryResource
    {
        $input = $request->all();
        $tableCategory = $this->tableCategoryRepository->storeTableCategory($input);

        TableCategoryResource::usingWithCollection();

        return new TableCategoryResource($tableCategory);
    }

    public function show($id): TableCategoryResource
    {
        $tableCategory = TableCategory::findOrFail($id);

        return new TableCategoryResource($tableCategory);
    }

    public function update(UpdateTableCategoryRequest $request, $id)
    {
        $input = $request->all();

        $brand = $this->tableCategoryRepository->updateTableCategory($input, $id);

        return new TableCategoryResource($brand);
    }

    public function destroy($id)
    {
        TableCategory::findOrFail($id)->delete();

        return $this->sendSuccess('Table Category deleted successfully');
    }
}
