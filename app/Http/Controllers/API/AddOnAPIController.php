<?php

namespace App\Http\Controllers\API;

use App\Models\AddOn;
use Illuminate\Http\Request;
use App\Http\Resources\AddOnResource;
use App\Repositories\AddOnRepository;
use App\Http\Resources\AddOnCollection;
use App\Http\Requests\CreateAddOnRequest;
use App\Http\Requests\UpdateAddOnRequest;
use App\Http\Controllers\AppBaseController;

class AddOnAPIController extends AppBaseController
{

    private $addOnRepository;

    public function __construct(AddOnRepository $addOnRepository)
    {
        $this->addOnRepository = $addOnRepository;
    }

    public function index()
    {
        $addOns = $this->addOnRepository->paginate(10);
        AddOnResource::usingWithCollection();

        return new AddOnCollection($addOns);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateAddOnRequest $request)
    {
        $input = $request->all();
        $data = $this->addOnRepository->storeAddOn($input);

        AddOnResource::usingWithCollection();

        return new AddOnResource($data);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $addOn = AddOn::findOrFail($id);

        return new AddOnResource($addOn);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAddOnRequest $request, $id)
    {
        $input = $request->all();

        $addOn = $this->addOnRepository->updateAddOn($input, $id);

        return new AddOnResource($addOn);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        AddOn::findOrFail($id)->delete();

        return $this->sendSuccess('Add On deleted successfully');
    }
}
