<?php

namespace App\Http\Controllers\API;

use App\Models\PickupPoint;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\PickupPointResource;
use App\Repositories\PickupPointRepository;
use App\Http\Resources\PickupPointCollection;
use App\Http\Requests\CreatePickupPointRequest;
use App\Http\Requests\UpdatePickupPointRequest;

class PickupPointAPIController extends AppBaseController
{
    private $pickupPointRepository;

    public function __construct(PickupPointRepository $pickupPointRepository)
    {
        $this->pickupPointRepository = $pickupPointRepository;
    }

    public function index(): PickupPointCollection
    {
        $pickupPoints = $this->pickupPointRepository->paginate(10);
        PickupPointResource::usingWithCollection();

        return new PickupPointCollection($pickupPoints);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreatePickupPointRequest $request)
    {
        $input = $request->all();
        $table = $this->pickupPointRepository->storePickupPoint($input);
        PickupPointResource::usingWithCollection();

        return new PickupPointResource($table);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $pickupPoint = PickupPoint::findOrFail($id);

        return new PickupPointResource($pickupPoint);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePickupPointRequest $request, $id)
    {
        $input = $request->all();

        $pickupPoint = $this->pickupPointRepository->updatePickupPoint($input, $id);

        return new PickupPointResource($pickupPoint);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        PickupPoint::findOrFail($id)->delete();

        return $this->sendSuccess('Pickup Point deleted successfully');
    }
}
