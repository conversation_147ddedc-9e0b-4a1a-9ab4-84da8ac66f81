<?php

namespace App\Http\Requests;

use App\Models\Brand;
use App\Models\TableCategory;
use Illuminate\Foundation\Http\FormRequest;

class CreateTableCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return TableCategory::rules();
    }
}
