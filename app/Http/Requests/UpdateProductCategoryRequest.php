<?php

namespace App\Http\Requests;

use App\Models\ProductCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateProductCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = ProductCategory::rules();
        $rules['name'] = 'required|unique:product_categories,name,' . $this->route('product_category') . ',id,tenant_id,' . Auth::user()->tenant_id;

        return $rules;
    }
}
