<?php

namespace App\Http\Requests;

use App\Models\Table;
use App\Models\TableCategory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CreateTableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = Table::rules();
        $rules['table_code'] = 'required|unique:tables,table_code,NULL,id,tenant_id,' . Auth::user()->tenant_id;
        
        return $rules;
    }
}
