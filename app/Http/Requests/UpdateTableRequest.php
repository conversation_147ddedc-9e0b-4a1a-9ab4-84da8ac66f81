<?php

namespace App\Http\Requests;

use App\Models\Table;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdateTableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $tableId = $this?->route('id');
        $rules = Table::rules();
        $rules['table_code'] = 'required|unique:tables,table_code,' . $tableId . ',id,tenant_id,' . Auth::user()->tenant_id;

        return $rules;
    }
}
