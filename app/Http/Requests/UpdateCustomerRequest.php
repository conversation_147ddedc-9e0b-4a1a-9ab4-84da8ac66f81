<?php

namespace App\Http\Requests;

use App\Models\Customer;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

/**
 * Class UpdateCustomerRequest
 */
class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = Customer::rules();
        $rules['email'] = 'required|email|unique:customers,email,' . $this->route('customer') . ',id,tenant_id,' . Auth::user()->tenant_id;

        return $rules;
    }

    public function messages(): array
    {
        $messages['email.unique'] = __('messages.error.email_taken');

        return $messages;
    }
}
