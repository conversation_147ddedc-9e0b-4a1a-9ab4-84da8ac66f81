<?php

namespace App\Repositories;

use App\Models\PickupPoint;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class BrandRepository
 */
class PickupPointRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'person_name',
        'address',
        'phone',
    ];

    /**
     * @var string[]
     */
    protected $allowedFields = [
        'name',
        'person_name',
        'address',
        'phone',
    ];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return PickupPoint::class;
    }

    public function storePickupPoint($input)
    {
        try {
            DB::beginTransaction();
            $data = $this->create($input);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function updatePickupPoint($input, $id)
    {
        try {
            DB::beginTransaction();
            $data = $this->update($input, $id);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
