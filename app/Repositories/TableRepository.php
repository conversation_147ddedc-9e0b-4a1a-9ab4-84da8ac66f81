<?php

namespace App\Repositories;

use App\Models\Table;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class BrandRepository
 */
class TableRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'table_name',
        'table_code',
        'max_seat_capacity',
        'table_category_id',
    ];

    /**
     * @var string[]
     */
    protected $allowedFields = [
        'table_name',
        'table_code',
        'max_seat_capacity',
        'table_category_id',
        'status',
    ];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Table::class;
    }

    public function storeTable($input)
    {
        try {
            DB::beginTransaction();
            $data = $this->create($input);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function updateTable($input, $id)
    {
        try {
            DB::beginTransaction();
            $data = $this->update($input, $id);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
