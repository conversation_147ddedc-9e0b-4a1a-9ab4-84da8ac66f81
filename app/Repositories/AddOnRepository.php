<?php

namespace App\Repositories;

use App\Models\AddOn;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class BrandRepository
 */
class AddOnRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'price',
    ];

    /**
     * @var string[]
     */
    protected $allowedFields = [
        'name',
        'price',
    ];

    /**
     * Return searchable fields
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return AddOn::class;
    }

    public function storeAddOn($input)
    {
        try {
            DB::beginTransaction();
            $data = $this->create($input);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function updateAddOn($input, $id)
    {
        try {
            DB::beginTransaction();
            $data = $this->update($input, $id);
            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
