{"private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "rtl": "node scripts/build-rtl.js"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.6.0", "laravel-vite-plugin": "^2.0.0", "lodash": "^4.17.19", "postcss": "^8.4.32", "postcss-rtlcss": "^5.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rtlcss": "^4.3.0", "sass": "1.77.6", "vite": "^7.0.4", "web-vitals": "^3.5.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.15.4", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fortawesome/react-fontawesome": "^0.2.2", "bootstrap": "^5.1.3", "bootstrap-icons": "^1.8.1", "chart.js": "^3.0.0", "del": "^6.1.1", "echarts": "^5.3.3", "echarts-for-react": "^3.0.2", "email-validator": "^2.0.4", "faker": "^5.5.3", "fortawesome": "^0.0.1-security", "history": "^5.0.0", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.11.20", "mini-css-extract-plugin": "^2.5.3", "moment": "^2.29.3", "prop-types": "^15.8.1", "react-bootstrap": "^2.1.2", "react-bootstrap-sweetalert": "^5.2.0", "react-bootstrap-v5": "^1.4.0", "react-chartjs-2": "^4.2.0", "react-data-table-component": "^7.4.7", "react-datepicker": "^4.7.0", "react-elastic-carousel": "^0.11.5", "react-helmet": "^6.1.0", "react-id-swiper": "^4.0.0", "react-image-lightbox": "^5.1.4", "react-intl": "^5.25.1", "react-phone-input-2": "^2.15.1", "react-pro-sidebar": "^0.7.1", "react-quill": "^2.0.0", "react-razorpay": "^3.0.1", "react-redux": "^7.2.6", "react-router": "^6.2.1", "react-router-dom": "^6.3.0", "react-search-autocomplete": "^7.2.2", "react-select": "^5.3.1", "react-to-print": "^2.14.6", "react-toastify": "^8.2.0", "react-topbar-progress-indicator": "^4.1.1", "reactstrap": "^9.0.2", "redux": "^4.1.2", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "rtlcss-webpack-plugin": "^4.0.7", "swiper": "^5.4.5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --fix"}}