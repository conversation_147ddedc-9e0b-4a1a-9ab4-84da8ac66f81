import { defineConfig } from "vite";
import { resolve } from "path";
import fs from "fs";
import rtlcss from "rtlcss";

// Custom RTL CSS Plugin
function rtlCssPlugin() {
    return {
        name: 'rtl-css-plugin',
        generateBundle(options, bundle) {
            // Process CSS files and generate RTL versions
            Object.keys(bundle).forEach(fileName => {
                if (fileName.endsWith('.css')) {
                    const cssAsset = bundle[fileName];
                    if (cssAsset.type === 'asset') {
                        // Generate RTL version
                        const rtlCss = rtlcss.process(cssAsset.source);
                        const rtlFileName = fileName.replace('.css', '.rtl.css');
                        
                        // Add RTL CSS as a new asset
                        this.emitFile({
                            type: 'asset',
                            fileName: rtlFileName,
                            source: rtlCss
                        });
                    }
                }
            });
        }
    };
}

export default defineConfig({
    plugins: [
        rtlCssPlugin(),
    ],
    build: {
        outDir: "resources/pos/src/assets/css",
        emptyOutDir: false,
        rollupOptions: {
            input: {
                'style': resolve(__dirname, 'resources/pos/src/assets/scss/style.scss'),
                'custom': resolve(__dirname, 'resources/pos/src/assets/scss/custom/custom.scss'),
                'frontend': resolve(__dirname, 'resources/pos/src/assets/scss/frontend/frontend.scss'),
            },
            output: {
                assetFileNames: '[name][extname]',
                entryFileNames: '[name].js',
            },
        },
    },
    css: {
        preprocessorOptions: {
            scss: {
                api: 'legacy',
                includePaths: [
                    resolve(__dirname, 'node_modules'),
                    resolve(__dirname, 'resources/pos/src/assets/scss'),
                    resolve(__dirname, 'resources/pos/src/assets/scss/components'),
                    resolve(__dirname, 'resources/pos/src/assets/scss/base'),
                    resolve(__dirname, 'resources/pos/src/assets/scss/layout'),
                    resolve(__dirname, 'resources/pos/src/assets/scss/custom'),
                    resolve(__dirname, 'resources/pos/src/assets/scss/frontend'),
                ],
            }
        }
    },
});
